[INFO] [2025-07-28T14:49:22.162Z] [user:system] [task:N/A] - SIGINT received, shutting down gracefully
[INFO] [2025-07-28T14:49:22.166Z] [user:system] [task:N/A] - All connections closed successfully
[INFO] [2025-07-28T14:49:25.439Z] [user:system] [task:N/A] - TTS Application started successfully {"port":"3001","environment":"development","websocketEndpoints":{"single":"ws://localhost:3001/api/tts/ws/generate","dialogue":"ws://localhost:3001/api/tts/ws/dialogue/generate"},"healthCheck":"http://localhost:3001/health"}
[INFO] [2025-07-28T14:49:58.958Z] [user:system] [task:N/A] - GET /quota {"status":401,"duration":"5ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHT<PERSON>, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::1"}
[INFO] [2025-07-28T14:49:59.080Z] [user:system] [task:N/A] - POST /refresh {"status":401,"duration":"3ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::1"}
[INFO] [2025-07-28T14:50:20.546Z] [user:system] [task:N/A] - POST /login {"status":200,"duration":"57ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::1"}
[INFO] [2025-07-28T14:50:22.010Z] [user:system] [task:N/A] - GET /quota {"status":200,"duration":"2ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::1"}
[INFO] [2025-07-28T14:50:27.741Z] [user:system] [task:N/A] - GET /quota {"status":200,"duration":"2ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::1"}
[INFO] [2025-07-28T14:50:31.448Z] [user:system] [task:N/A] - GET /quota {"status":200,"duration":"2ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::1"}
[INFO] [2025-07-28T14:53:20.199Z] [user:system] [task:N/A] - Dialogue TTS WebSocket connection established {"ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
[INFO] [2025-07-28T14:53:21.524Z] [user:system] [task:N/A] - [TTS-ROUTE] Route decision: gateway {"decision":"gateway","networkMode":"gateway","gatewayEnabled":true}
[INFO] [2025-07-28T14:53:21.528Z] [user:system] [task:N/A] - [TTS-GATEWAY] Starting gateway request {"action":"Starting gateway request","textLength":36,"voiceId":"tapn1QwocNXk3viVSowa","modelId":"eleven_v3"}
[INFO] [2025-07-28T14:53:21.529Z] [user:system] [task:N/A] - [TTS-GATEWAY] Sending request via network manager {"action":"Sending request via network manager","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","payloadSize":154}
[INFO] [2025-07-28T14:53:21.531Z] [user:system] [task:N/A] - [NETWORK-MANAGER] Initializing NetworkManager...
[DEBUG] [2025-07-28T14:53:21.532Z] [user:system] [task:N/A] - [NETWORK-MANAGER] Creating network client for mode: gateway
[INFO] [2025-07-28T14:53:21.543Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Initializing ProxyGateway...
[DEBUG] [2025-07-28T14:53:21.543Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Initializing WorkerPoolController...
[INFO] [2025-07-28T14:53:21.544Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker pool initialized {"poolSize":10,"portRange":"1081-1090","selectorPrefix":"worker-selector"}
[INFO] [2025-07-28T14:53:21.545Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Initializing WorkerPoolController with Clash API...
[DEBUG] [2025-07-28T14:53:21.546Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Loading nodes from sing-box Clash API...
[INFO] [2025-07-28T14:53:21.573Z] [user:system] [task:N/A] - [TTS-ROUTE] Route decision: gateway {"decision":"gateway","networkMode":"gateway","gatewayEnabled":true}
[INFO] [2025-07-28T14:53:21.574Z] [user:system] [task:N/A] - [TTS-GATEWAY] Starting gateway request {"action":"Starting gateway request","textLength":22,"voiceId":"WrPknjKhmIXkCONEtG3j","modelId":"eleven_v3"}
[INFO] [2025-07-28T14:53:21.574Z] [user:system] [task:N/A] - [TTS-GATEWAY] Sending request via network manager {"action":"Sending request via network manager","url":"https://api.elevenlabs.io/v1/text-to-speech/WrPknjKhmIXkCONEtG3j?allow_unauthenticated=1","payloadSize":140}
[WARN] [2025-07-28T14:53:21.582Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Skipping invalid node name: tw-tw-idx-97
[WARN] [2025-07-28T14:53:21.583Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Skipping invalid node name: sg-sg-idx-114
[WARN] [2025-07-28T14:53:21.584Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Skipping invalid node name: jp-jp-idx-118
[INFO] [2025-07-28T14:53:21.584Z] [user:system] [task:N/A] - [TTS-NODE] Loaded nodes for worker pool {"action":"Loaded nodes for worker pool","totalNodes":116,"validNodes":116,"nodeList":["sg-sg-013x-idx-0","sg-sg-023x-idx-1","sg-sg-033x-idx-2","sg-sg-043x-idx-3","sg-sg-053x-idx-4","sg-sg-063x-idx-5","sg-sg-073x-idx-6","sg-sg-083x-idx-7","sg-sg-093x-idx-8","jp-jp-013x-idx-9","...106 more"],"validNodeDetails":[{"name":"sg-sg-013x-idx-0","type":"Shadowsocks"},{"name":"sg-sg-023x-idx-1","type":"Shadowsocks"},{"name":"sg-sg-033x-idx-2","type":"Shadowsocks"},{"name":"sg-sg-043x-idx-3","type":"Trojan"},{"name":"sg-sg-053x-idx-4","type":"Trojan"},{"name":"sg-sg-063x-idx-5","type":"Trojan"},{"name":"sg-sg-073x-idx-6","type":"Trojan"},{"name":"sg-sg-083x-idx-7","type":"Trojan"},{"name":"sg-sg-093x-idx-8","type":"Trojan"},{"name":"jp-jp-013x-idx-9","type":"Shadowsocks"},{"name":"...106 more nodes","type":"truncated"}]}
[INFO] [2025-07-28T14:53:21.585Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Loaded nodes from sing-box Clash API {"totalNodes":116,"validNodes":116,"nodeList":["sg-sg-013x-idx-0","sg-sg-023x-idx-1","sg-sg-033x-idx-2","sg-sg-043x-idx-3","sg-sg-053x-idx-4","sg-sg-063x-idx-5","sg-sg-073x-idx-6","sg-sg-083x-idx-7","sg-sg-093x-idx-8","jp-jp-013x-idx-9","...106 more"]}
[INFO] [2025-07-28T14:53:21.592Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Loaded quarantine data for 2 nodes
[DEBUG] [2025-07-28T14:53:21.592Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Validating worker selectors...
[INFO] [2025-07-28T14:53:21.607Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] All worker selectors validated successfully
[INFO] [2025-07-28T14:53:21.608Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] WorkerPoolController initialized successfully {"totalNodes":116,"healthyNodes":116,"quarantinedNodes":0,"totalWorkers":10,"apiType":"clash"}
[DEBUG] [2025-07-28T14:53:21.609Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] WorkerPoolController created and initialized (singleton)
[DEBUG] [2025-07-28T14:53:21.609Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Initializing NetworkAdapter...
[DEBUG] [2025-07-28T14:53:21.609Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] NetworkAdapter initialized
[INFO] [2025-07-28T14:53:21.610Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Starting health check with 30000ms interval
[INFO] [2025-07-28T14:53:21.610Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Starting quarantine check with 600000ms interval
[INFO] [2025-07-28T14:53:21.611Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] ProxyGateway initialized successfully {"mode":"gateway","singboxEnabled":true,"healthCheckEnabled":true}
[DEBUG] [2025-07-28T14:53:21.611Z] [user:system] [task:N/A] - [NETWORK-MANAGER] Network client created for mode: gateway
[INFO] [2025-07-28T14:53:21.612Z] [user:system] [task:N/A] - [NETWORK-MANAGER] NetworkManager initialized successfully {"mode":"gateway","gatewayEnabled":true}
[DEBUG] [2025-07-28T14:53:21.612Z] [user:system] [task:N/A] - [NETWORK-MANAGER] Making network request {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","mode":"gateway"}
[DEBUG] [2025-07-28T14:53:21.613Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Making gateway request {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","mode":"gateway"}
[DEBUG] [2025-07-28T14:53:21.615Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Testing node sg-sg-013x-idx-0 (attempt 1/114)
[DEBUG] [2025-07-28T14:53:21.616Z] [user:system] [task:N/A] - [NETWORK-MANAGER] Making network request {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/WrPknjKhmIXkCONEtG3j?allow_unauthenticated=1","mode":"gateway"}
[DEBUG] [2025-07-28T14:53:21.616Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Making gateway request {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/WrPknjKhmIXkCONEtG3j?allow_unauthenticated=1","mode":"gateway"}
[DEBUG] [2025-07-28T14:53:21.617Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Testing node sg-sg-023x-idx-1 (attempt 1/114)
[DEBUG] [2025-07-28T14:53:21.754Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node sg-sg-013x-idx-0 {"nodeTag":"sg-sg-013x-idx-0","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-28T14:53:21.754Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Node sg-sg-013x-idx-0 passed health check
[DEBUG] [2025-07-28T14:53:21.755Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Commanding worker selector worker-selector-1 to switch to node: sg-sg-013x-idx-0
[INFO] [2025-07-28T14:53:21.755Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-1","targetNode":"sg-sg-013x-idx-0","originalNodeTag":"sg-sg-013x-idx-0","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-1","requestBody":{"name":"sg-sg-013x-idx-0"}}
[DEBUG] [2025-07-28T14:53:21.764Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node sg-sg-023x-idx-1 {"nodeTag":"sg-sg-023x-idx-1","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-28T14:53:21.765Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Node sg-sg-023x-idx-1 passed health check
[DEBUG] [2025-07-28T14:53:21.765Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Commanding worker selector worker-selector-3 to switch to node: sg-sg-023x-idx-1
[INFO] [2025-07-28T14:53:21.766Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-3","targetNode":"sg-sg-023x-idx-1","originalNodeTag":"sg-sg-023x-idx-1","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-3","requestBody":{"name":"sg-sg-023x-idx-1"}}
[DEBUG] [2025-07-28T14:53:21.767Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Empty response from PUT /proxies/worker-selector-1
[INFO] [2025-07-28T14:53:21.767Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-1","newNode":"sg-sg-013x-idx-0","fixedNodeTag":"sg-sg-013x-idx-0"}
[DEBUG] [2025-07-28T14:53:21.768Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker selector worker-selector-1 successfully switched to node: sg-sg-013x-idx-0
[DEBUG] [2025-07-28T14:53:21.768Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker acquired with lazy health check {"workerId":1,"port":1081,"selector":"worker-selector-1","assignedNode":"sg-sg-013x-idx-0"}
[INFO] [2025-07-28T14:53:21.769Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":1,"workerPort":1081,"assignedNode":"sg-sg-013x-idx-0","method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","attempt":1,"maxAttempts":2}
[INFO] [2025-07-28T14:53:21.971Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1081","workerId":1,"assignedNode":"sg-sg-013x-idx-0","method":"POST","hasProxy":true,"proxyType":"dispatcher","attempt":1}
[DEBUG] [2025-07-28T14:53:21.976Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Empty response from PUT /proxies/worker-selector-3
[INFO] [2025-07-28T14:53:21.977Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-3","newNode":"sg-sg-023x-idx-1","fixedNodeTag":"sg-sg-023x-idx-1"}
[DEBUG] [2025-07-28T14:53:21.977Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker selector worker-selector-3 successfully switched to node: sg-sg-023x-idx-1
[DEBUG] [2025-07-28T14:53:21.977Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker acquired with lazy health check {"workerId":3,"port":1083,"selector":"worker-selector-3","assignedNode":"sg-sg-023x-idx-1"}
[INFO] [2025-07-28T14:53:21.978Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":3,"workerPort":1083,"assignedNode":"sg-sg-023x-idx-1","method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/WrPknjKhmIXkCONEtG3j?allow_unauthenticated=1","attempt":1,"maxAttempts":2}
[INFO] [2025-07-28T14:53:21.978Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1083","workerId":3,"assignedNode":"sg-sg-023x-idx-1","method":"POST","hasProxy":true,"proxyType":"dispatcher","attempt":1}
[INFO] [2025-07-28T14:53:24.602Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker pool response received {"action":"Worker pool response received","status":200,"ok":true,"workerId":3,"assignedNode":"sg-sg-023x-idx-1","attempt":1}
[DEBUG] [2025-07-28T14:53:24.603Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker released {"workerId":3,"port":1083,"currentNode":"sg-sg-023x-idx-1"}
[INFO] [2025-07-28T14:53:24.604Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":3,"assignedNode":"sg-sg-023x-idx-1","attempt":1}
[DEBUG] [2025-07-28T14:53:24.604Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Network request: POST https://api.elevenlabs.io/v1/text-to-speech/WrPknjKhmIXkCONEtG3j?allow_unauthenticated=1 {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/WrPknjKhmIXkCONEtG3j?allow_unauthenticated=1","status":200,"duration":"2988ms"}
[INFO] [2025-07-28T14:53:24.605Z] [user:system] [task:N/A] - [TTS-NETWORK] POST https://api.elevenlabs.io/v1/text-to-speech/WrPknjKhmIXkCONEtG3j {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/WrPknjKhmIXkCONEtG3j","status":200,"duration":"3030ms","responseOk":true}
[INFO] [2025-07-28T14:53:24.682Z] [user:system] [task:N/A] - [TTS-GATEWAY] Request completed successfully {"action":"Request completed successfully","audioSize":31391,"totalDuration":"3107ms","requestDuration":"3030ms"}
[INFO] [2025-07-28T14:53:24.682Z] [user:system] [task:N/A] - [TTS-SUCCESS] Generated via gateway {"mode":"gateway","audioSize":31391,"duration":"3109ms"}
[INFO] [2025-07-28T14:53:24.683Z] [user:system] [task:N/A] - [TTS-ROUTE] Route decision: gateway {"decision":"gateway","networkMode":"gateway","gatewayEnabled":true}
[INFO] [2025-07-28T14:53:24.683Z] [user:system] [task:N/A] - [TTS-GATEWAY] Starting gateway request {"action":"Starting gateway request","textLength":10,"voiceId":"WrPknjKhmIXkCONEtG3j","modelId":"eleven_v3"}
[INFO] [2025-07-28T14:53:24.684Z] [user:system] [task:N/A] - [TTS-GATEWAY] Sending request via network manager {"action":"Sending request via network manager","url":"https://api.elevenlabs.io/v1/text-to-speech/WrPknjKhmIXkCONEtG3j?allow_unauthenticated=1","payloadSize":128}
[DEBUG] [2025-07-28T14:53:24.684Z] [user:system] [task:N/A] - [NETWORK-MANAGER] Making network request {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/WrPknjKhmIXkCONEtG3j?allow_unauthenticated=1","mode":"gateway"}
[DEBUG] [2025-07-28T14:53:24.685Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Making gateway request {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/WrPknjKhmIXkCONEtG3j?allow_unauthenticated=1","mode":"gateway"}
[DEBUG] [2025-07-28T14:53:24.685Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Testing node sg-sg-033x-idx-2 (attempt 1/114)
[DEBUG] [2025-07-28T14:53:24.772Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node sg-sg-033x-idx-2 {"nodeTag":"sg-sg-033x-idx-2","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-28T14:53:24.773Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Node sg-sg-033x-idx-2 passed health check
[DEBUG] [2025-07-28T14:53:24.774Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Commanding worker selector worker-selector-4 to switch to node: sg-sg-033x-idx-2
[INFO] [2025-07-28T14:53:24.774Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-4","targetNode":"sg-sg-033x-idx-2","originalNodeTag":"sg-sg-033x-idx-2","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-4","requestBody":{"name":"sg-sg-033x-idx-2"}}
[DEBUG] [2025-07-28T14:53:24.775Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Empty response from PUT /proxies/worker-selector-4
[INFO] [2025-07-28T14:53:24.776Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-4","newNode":"sg-sg-033x-idx-2","fixedNodeTag":"sg-sg-033x-idx-2"}
[DEBUG] [2025-07-28T14:53:24.776Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker selector worker-selector-4 successfully switched to node: sg-sg-033x-idx-2
[DEBUG] [2025-07-28T14:53:24.776Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker acquired with lazy health check {"workerId":4,"port":1084,"selector":"worker-selector-4","assignedNode":"sg-sg-033x-idx-2"}
[INFO] [2025-07-28T14:53:24.777Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":4,"workerPort":1084,"assignedNode":"sg-sg-033x-idx-2","method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/WrPknjKhmIXkCONEtG3j?allow_unauthenticated=1","attempt":1,"maxAttempts":2}
[INFO] [2025-07-28T14:53:24.777Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1084","workerId":4,"assignedNode":"sg-sg-033x-idx-2","method":"POST","hasProxy":true,"proxyType":"dispatcher","attempt":1}
[DEBUG] [2025-07-28T14:53:26.615Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Starting quarantine pool check...
[INFO] [2025-07-28T14:53:26.616Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Checking 2 quarantined nodes
[DEBUG] [2025-07-28T14:53:26.616Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Checking node: sg-sg-053x-idx-4 (temporary)
[DEBUG] [2025-07-28T14:53:26.703Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node sg-sg-053x-idx-4 {"nodeTag":"sg-sg-053x-idx-4","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-28T14:53:26.704Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Node sg-sg-053x-idx-4 health check passed but needs more successes {"consecutiveSuccesses":1,"requiredSuccesses":2,"quarantineType":"temporary"}
[DEBUG] [2025-07-28T14:53:26.704Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Node sg-sg-053x-idx-4 health check passed but needs more successes
[DEBUG] [2025-07-28T14:53:26.704Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Checking node: sg-sg-083x-idx-7 (temporary)
[DEBUG] [2025-07-28T14:53:26.833Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node sg-sg-083x-idx-7 {"nodeTag":"sg-sg-083x-idx-7","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-28T14:53:26.834Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Node sg-sg-083x-idx-7 health check passed but needs more successes {"consecutiveSuccesses":1,"requiredSuccesses":2,"quarantineType":"temporary"}
[DEBUG] [2025-07-28T14:53:26.834Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Node sg-sg-083x-idx-7 health check passed but needs more successes
[INFO] [2025-07-28T14:53:26.834Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Check completed: 2 checked, 0 recovered
[INFO] [2025-07-28T14:53:27.318Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker pool response received {"action":"Worker pool response received","status":200,"ok":true,"workerId":4,"assignedNode":"sg-sg-033x-idx-2","attempt":1}
[DEBUG] [2025-07-28T14:53:27.319Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker released {"workerId":4,"port":1084,"currentNode":"sg-sg-033x-idx-2"}
[INFO] [2025-07-28T14:53:27.319Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":4,"assignedNode":"sg-sg-033x-idx-2","attempt":1}
[DEBUG] [2025-07-28T14:53:27.320Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Network request: POST https://api.elevenlabs.io/v1/text-to-speech/WrPknjKhmIXkCONEtG3j?allow_unauthenticated=1 {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/WrPknjKhmIXkCONEtG3j?allow_unauthenticated=1","status":200,"duration":"2635ms"}
[INFO] [2025-07-28T14:53:27.320Z] [user:system] [task:N/A] - [TTS-NETWORK] POST https://api.elevenlabs.io/v1/text-to-speech/WrPknjKhmIXkCONEtG3j {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/WrPknjKhmIXkCONEtG3j","status":200,"duration":"2636ms","responseOk":true}
[INFO] [2025-07-28T14:53:27.372Z] [user:system] [task:N/A] - [TTS-GATEWAY] Request completed successfully {"action":"Request completed successfully","audioSize":17181,"totalDuration":"2689ms","requestDuration":"2636ms"}
[INFO] [2025-07-28T14:53:27.372Z] [user:system] [task:N/A] - [TTS-SUCCESS] Generated via gateway {"mode":"gateway","audioSize":17181,"duration":"2689ms"}
[INFO] [2025-07-28T14:53:28.309Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker pool response received {"action":"Worker pool response received","status":200,"ok":true,"workerId":1,"assignedNode":"sg-sg-013x-idx-0","attempt":1}
[DEBUG] [2025-07-28T14:53:28.309Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker released {"workerId":1,"port":1081,"currentNode":"sg-sg-013x-idx-0"}
[INFO] [2025-07-28T14:53:28.310Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":1,"assignedNode":"sg-sg-013x-idx-0","attempt":1}
[WARN] [2025-07-28T14:53:28.310Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Slow network request: POST https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1 {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","status":200,"duration":"6697ms"}
[INFO] [2025-07-28T14:53:28.310Z] [user:system] [task:N/A] - [TTS-NETWORK] POST https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa","status":200,"duration":"6781ms","responseOk":true}
[INFO] [2025-07-28T14:53:28.492Z] [user:system] [task:N/A] - [TTS-GATEWAY] Request completed successfully {"action":"Request completed successfully","audioSize":102862,"totalDuration":"6967ms","requestDuration":"6781ms"}
[INFO] [2025-07-28T14:53:28.492Z] [user:system] [task:N/A] - [TTS-SUCCESS] Generated via gateway {"mode":"gateway","audioSize":102862,"duration":"6970ms"}
[INFO] [2025-07-28T14:53:28.493Z] [user:system] [task:N/A] - [TTS-ROUTE] Route decision: gateway {"decision":"gateway","networkMode":"gateway","gatewayEnabled":true}
[INFO] [2025-07-28T14:53:28.494Z] [user:system] [task:N/A] - [TTS-GATEWAY] Starting gateway request {"action":"Starting gateway request","textLength":57,"voiceId":"tapn1QwocNXk3viVSowa","modelId":"eleven_v3"}
[INFO] [2025-07-28T14:53:28.494Z] [user:system] [task:N/A] - [TTS-GATEWAY] Sending request via network manager {"action":"Sending request via network manager","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","payloadSize":175}
[DEBUG] [2025-07-28T14:53:28.494Z] [user:system] [task:N/A] - [NETWORK-MANAGER] Making network request {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","mode":"gateway"}
[DEBUG] [2025-07-28T14:53:28.495Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Making gateway request {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","mode":"gateway"}
[DEBUG] [2025-07-28T14:53:28.495Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Testing node sg-sg-043x-idx-3 (attempt 1/114)
[DEBUG] [2025-07-28T14:53:28.582Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node sg-sg-043x-idx-3 {"nodeTag":"sg-sg-043x-idx-3","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-28T14:53:28.583Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Node sg-sg-043x-idx-3 passed health check
[DEBUG] [2025-07-28T14:53:28.583Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Commanding worker selector worker-selector-4 to switch to node: sg-sg-043x-idx-3
[INFO] [2025-07-28T14:53:28.583Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-4","targetNode":"sg-sg-043x-idx-3","originalNodeTag":"sg-sg-043x-idx-3","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-4","requestBody":{"name":"sg-sg-043x-idx-3"}}
[DEBUG] [2025-07-28T14:53:28.584Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Empty response from PUT /proxies/worker-selector-4
[INFO] [2025-07-28T14:53:28.585Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-4","newNode":"sg-sg-043x-idx-3","fixedNodeTag":"sg-sg-043x-idx-3"}
[DEBUG] [2025-07-28T14:53:28.585Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker selector worker-selector-4 successfully switched to node: sg-sg-043x-idx-3
[DEBUG] [2025-07-28T14:53:28.585Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker acquired with lazy health check {"workerId":4,"port":1084,"selector":"worker-selector-4","assignedNode":"sg-sg-043x-idx-3"}
[INFO] [2025-07-28T14:53:28.586Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":4,"workerPort":1084,"assignedNode":"sg-sg-043x-idx-3","method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","attempt":1,"maxAttempts":2}
[INFO] [2025-07-28T14:53:28.586Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1084","workerId":4,"assignedNode":"sg-sg-043x-idx-3","method":"POST","hasProxy":true,"proxyType":"dispatcher","attempt":1}
[ERROR] [2025-07-28T14:53:28.915Z] [user:system] [task:N/A] - [TTS-ERROR] Failed in worker-pool-request mode {"mode":"worker-pool-request","error":"fetch failed","method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","workerId":4,"assignedNode":"sg-sg-043x-idx-3","attempt":1,"maxAttempts":2} {"mode":"worker-pool-request","error":"[TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"POST\",\"url\":\"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1\",\"workerId\":4,\"assignedNode\":\"sg-sg-043x-idx-3\",\"attempt\":1,\"maxAttempts\":2}","method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","workerId":4,"assignedNode":"sg-sg-043x-idx-3","attempt":1,"maxAttempts":2,"stack":"Error: [TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"POST\",\"url\":\"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1\",\"workerId\":4,\"assignedNode\":\"sg-sg-043x-idx-3\",\"attempt\":1,\"maxAttempts\":2}\n    at TTSRouteLogger.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:270:18)\n    at Object.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:303:48)\n    at NetworkAdapter.requestViaGateway (D:\\myai"}
[WARN] [2025-07-28T14:53:28.916Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Worker pool network connectivity error {"error":"fetch failed","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","workerId":4,"nodeTag":"sg-sg-043x-idx-3"}
[WARN] [2025-07-28T14:53:28.917Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Moving failed node to quarantine: sg-sg-043x-idx-3 {"reason":"Network error: fetch failed"}
[INFO] [2025-07-28T14:53:28.917Z] [user:system] [task:N/A] - [TTS-NODE] Node moved to quarantine {"action":"Node moved to quarantine","nodeTag":"sg-sg-043x-idx-3","reason":"Network error: fetch failed","quarantineType":"temporary","remainingHealthyNodes":113,"totalQuarantinedNodes":3}
[INFO] [2025-07-28T14:53:28.918Z] [user:system] [task:N/A] - [TTS-GATEWAY] Gateway request failed, retrying with new node {"action":"Gateway request failed, retrying with new node","attempt":1,"maxAttempts":2,"error":"fetch failed","failedNode":"sg-sg-043x-idx-3","willRetry":true}
[DEBUG] [2025-07-28T14:53:28.918Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker released {"workerId":4,"port":1084,"currentNode":"sg-sg-043x-idx-3"}
[INFO] [2025-07-28T14:53:28.918Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":4,"assignedNode":"sg-sg-043x-idx-3","attempt":1}
[DEBUG] [2025-07-28T14:53:28.919Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Testing node sg-sg-073x-idx-6 (attempt 1/113)
[DEBUG] [2025-07-28T14:53:28.920Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Saved quarantine data for 3 nodes
[DEBUG] [2025-07-28T14:53:29.006Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node sg-sg-073x-idx-6 {"nodeTag":"sg-sg-073x-idx-6","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-28T14:53:29.007Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Node sg-sg-073x-idx-6 passed health check
[DEBUG] [2025-07-28T14:53:29.008Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Commanding worker selector worker-selector-5 to switch to node: sg-sg-073x-idx-6
[INFO] [2025-07-28T14:53:29.008Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-5","targetNode":"sg-sg-073x-idx-6","originalNodeTag":"sg-sg-073x-idx-6","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-5","requestBody":{"name":"sg-sg-073x-idx-6"}}
[DEBUG] [2025-07-28T14:53:29.010Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Empty response from PUT /proxies/worker-selector-5
[INFO] [2025-07-28T14:53:29.010Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-5","newNode":"sg-sg-073x-idx-6","fixedNodeTag":"sg-sg-073x-idx-6"}
[DEBUG] [2025-07-28T14:53:29.011Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker selector worker-selector-5 successfully switched to node: sg-sg-073x-idx-6
[DEBUG] [2025-07-28T14:53:29.011Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker acquired with lazy health check {"workerId":5,"port":1085,"selector":"worker-selector-5","assignedNode":"sg-sg-073x-idx-6"}
[INFO] [2025-07-28T14:53:29.012Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":5,"workerPort":1085,"assignedNode":"sg-sg-073x-idx-6","method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","attempt":2,"maxAttempts":2}
[INFO] [2025-07-28T14:53:29.012Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1085","workerId":5,"assignedNode":"sg-sg-073x-idx-6","method":"POST","hasProxy":true,"proxyType":"dispatcher","attempt":2}
[ERROR] [2025-07-28T14:53:29.046Z] [user:system] [task:N/A] - [TTS-ERROR] Failed in worker-pool-request mode {"mode":"worker-pool-request","error":"fetch failed","method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","workerId":5,"assignedNode":"sg-sg-073x-idx-6","attempt":2,"maxAttempts":2} {"mode":"worker-pool-request","error":"[TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"POST\",\"url\":\"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1\",\"workerId\":5,\"assignedNode\":\"sg-sg-073x-idx-6\",\"attempt\":2,\"maxAttempts\":2}","method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","workerId":5,"assignedNode":"sg-sg-073x-idx-6","attempt":2,"maxAttempts":2,"stack":"Error: [TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"POST\",\"url\":\"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1\",\"workerId\":5,\"assignedNode\":\"sg-sg-073x-idx-6\",\"attempt\":2,\"maxAttempts\":2}\n    at TTSRouteLogger.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:270:18)\n    at Object.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:303:48)\n    at NetworkAdapter.requestViaGateway (D:\\myai"}
[WARN] [2025-07-28T14:53:29.047Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Worker pool network connectivity error {"error":"fetch failed","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","workerId":5,"nodeTag":"sg-sg-073x-idx-6"}
[WARN] [2025-07-28T14:53:29.048Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Moving failed node to quarantine: sg-sg-073x-idx-6 {"reason":"Network error: fetch failed"}
[INFO] [2025-07-28T14:53:29.048Z] [user:system] [task:N/A] - [TTS-NODE] Node moved to quarantine {"action":"Node moved to quarantine","nodeTag":"sg-sg-073x-idx-6","reason":"Network error: fetch failed","quarantineType":"temporary","remainingHealthyNodes":112,"totalQuarantinedNodes":4}
[DEBUG] [2025-07-28T14:53:29.049Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker released {"workerId":5,"port":1085,"currentNode":"sg-sg-073x-idx-6"}
[INFO] [2025-07-28T14:53:29.049Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":5,"assignedNode":"sg-sg-073x-idx-6","attempt":2}
[ERROR] [2025-07-28T14:53:29.049Z] [user:system] [task:N/A] - Unknown error {"error":"Unknown error"}
[ERROR] [2025-07-28T14:53:29.050Z] [user:system] [task:N/A] - Unknown error {"error":"Unknown error"}
[ERROR] [2025-07-28T14:53:29.050Z] [user:system] [task:N/A] - [TTS-ERROR] Failed in gateway mode {"mode":"gateway","error":"fetch failed","totalDuration":"556ms"} {"mode":"gateway","error":"[TTS-ERROR] Failed in gateway mode {\"mode\":\"gateway\",\"error\":\"fetch failed\",\"totalDuration\":\"556ms\"}","totalDuration":"556ms","stack":"Error: [TTS-ERROR] Failed in gateway mode {\"mode\":\"gateway\",\"error\":\"fetch failed\",\"totalDuration\":\"556ms\"}\n    at TTSRouteLogger.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:270:18)\n    at Object.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:303:48)\n    at generateSpeechWithGateway (D:\\myaitts\\backend\\src\\utils\\ttsUtils.js:1322:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateSpeechSmart (D:\\myaitts\\backend\\src\\utils\\ttsUti"}
[ERROR] [2025-07-28T14:53:29.050Z] [user:system] [task:N/A] - [TTS-ERROR] Failed in smart mode {"mode":"smart","error":"fetch failed","duration":"557ms"} {"mode":"smart","error":"[TTS-ERROR] Failed in smart mode {\"mode\":\"smart\",\"error\":\"fetch failed\",\"duration\":\"557ms\"}","duration":"557ms","stack":"Error: [TTS-ERROR] Failed in smart mode {\"mode\":\"smart\",\"error\":\"fetch failed\",\"duration\":\"557ms\"}\n    at TTSRouteLogger.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:270:18)\n    at Object.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:303:48)\n    at generateSpeechSmart (D:\\myaitts\\backend\\src\\utils\\ttsUtils.js:1462:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\myaitts\\backend\\src\\utils\\ttsUtils.js:310:29"}
[DEBUG] [2025-07-28T14:53:29.052Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Saved quarantine data for 4 nodes
[INFO] [2025-07-28T14:53:31.053Z] [user:system] [task:N/A] - [TTS-PROXY] Using fallback mode {"action":"Using fallback mode","strategy":"direct-first-then-proxy"}
[INFO] [2025-07-28T14:53:31.053Z] [user:system] [task:N/A] - [TTS-DIRECT] Attempting direct connection first {"action":"Attempting direct connection first"}
[INFO] [2025-07-28T14:53:31.054Z] [user:system] [task:N/A] - [TTS-DIRECT] Starting direct ElevenLabs request {"action":"Starting direct ElevenLabs request","voiceId":"tapn1QwocNXk3viVSowa","modelId":"eleven_v3","textLength":57}
[INFO] [2025-07-28T14:53:42.863Z] [user:system] [task:N/A] - [TTS-NETWORK] POST https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa","status":200,"duration":"11809ms","responseOk":true}
[INFO] [2025-07-28T14:53:43.894Z] [user:system] [task:N/A] - [TTS-DIRECT] Direct request successful {"action":"Direct request successful","audioSize":196485,"requestDuration":"11809ms","totalDuration":"12840ms"}
[INFO] [2025-07-28T14:53:43.895Z] [user:system] [task:N/A] - [TTS-SUCCESS] Generated via direct-fallback {"mode":"direct-fallback","audioSize":196485,"duration":"12843ms"}
[INFO] [2025-07-28T14:53:43.897Z] [user:system] [task:N/A] - [TTS-ROUTE] Route decision: gateway {"decision":"gateway","networkMode":"gateway","gatewayEnabled":true}
[INFO] [2025-07-28T14:53:43.897Z] [user:system] [task:N/A] - [TTS-GATEWAY] Starting gateway request {"action":"Starting gateway request","textLength":191,"voiceId":"tapn1QwocNXk3viVSowa","modelId":"eleven_v3"}
[INFO] [2025-07-28T14:53:43.897Z] [user:system] [task:N/A] - [TTS-GATEWAY] Sending request via network manager {"action":"Sending request via network manager","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","payloadSize":309}
[DEBUG] [2025-07-28T14:53:43.897Z] [user:system] [task:N/A] - [NETWORK-MANAGER] Making network request {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","mode":"gateway"}
[DEBUG] [2025-07-28T14:53:43.898Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Making gateway request {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","mode":"gateway"}
[DEBUG] [2025-07-28T14:53:43.898Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Testing node jp-jp-013x-idx-9 (attempt 1/112)
[DEBUG] [2025-07-28T14:53:44.030Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node jp-jp-013x-idx-9 {"nodeTag":"jp-jp-013x-idx-9","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-28T14:53:44.031Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Node jp-jp-013x-idx-9 passed health check
[DEBUG] [2025-07-28T14:53:44.031Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Commanding worker selector worker-selector-6 to switch to node: jp-jp-013x-idx-9
[INFO] [2025-07-28T14:53:44.031Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-6","targetNode":"jp-jp-013x-idx-9","originalNodeTag":"jp-jp-013x-idx-9","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-6","requestBody":{"name":"jp-jp-013x-idx-9"}}
[DEBUG] [2025-07-28T14:53:44.033Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Empty response from PUT /proxies/worker-selector-6
[INFO] [2025-07-28T14:53:44.033Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-6","newNode":"jp-jp-013x-idx-9","fixedNodeTag":"jp-jp-013x-idx-9"}
[DEBUG] [2025-07-28T14:53:44.034Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker selector worker-selector-6 successfully switched to node: jp-jp-013x-idx-9
[DEBUG] [2025-07-28T14:53:44.034Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker acquired with lazy health check {"workerId":6,"port":1086,"selector":"worker-selector-6","assignedNode":"jp-jp-013x-idx-9"}
[INFO] [2025-07-28T14:53:44.034Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":6,"workerPort":1086,"assignedNode":"jp-jp-013x-idx-9","method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","attempt":1,"maxAttempts":2}
[INFO] [2025-07-28T14:53:44.035Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1086","workerId":6,"assignedNode":"jp-jp-013x-idx-9","method":"POST","hasProxy":true,"proxyType":"dispatcher","attempt":1}
[DEBUG] [2025-07-28T14:53:51.626Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Making gateway request {"method":"GET","url":"https://httpbin.org/ip","mode":"gateway"}
[DEBUG] [2025-07-28T14:53:51.627Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Testing node jp-jp-023x-idx-10 (attempt 1/112)
[DEBUG] [2025-07-28T14:53:51.773Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node jp-jp-023x-idx-10 {"nodeTag":"jp-jp-023x-idx-10","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-28T14:53:51.773Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Node jp-jp-023x-idx-10 passed health check
[DEBUG] [2025-07-28T14:53:51.774Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Commanding worker selector worker-selector-8 to switch to node: jp-jp-023x-idx-10
[INFO] [2025-07-28T14:53:51.774Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-8","targetNode":"jp-jp-023x-idx-10","originalNodeTag":"jp-jp-023x-idx-10","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-8","requestBody":{"name":"jp-jp-023x-idx-10"}}
[DEBUG] [2025-07-28T14:53:51.776Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Empty response from PUT /proxies/worker-selector-8
[INFO] [2025-07-28T14:53:51.776Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-8","newNode":"jp-jp-023x-idx-10","fixedNodeTag":"jp-jp-023x-idx-10"}
[DEBUG] [2025-07-28T14:53:51.777Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker selector worker-selector-8 successfully switched to node: jp-jp-023x-idx-10
[DEBUG] [2025-07-28T14:53:51.777Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker acquired with lazy health check {"workerId":8,"port":1088,"selector":"worker-selector-8","assignedNode":"jp-jp-023x-idx-10"}
[INFO] [2025-07-28T14:53:51.778Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":8,"workerPort":1088,"assignedNode":"jp-jp-023x-idx-10","method":"GET","url":"https://httpbin.org/ip","attempt":1,"maxAttempts":2}
[INFO] [2025-07-28T14:53:51.778Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1088","workerId":8,"assignedNode":"jp-jp-023x-idx-10","method":"GET","hasProxy":true,"proxyType":"dispatcher","attempt":1}
[INFO] [2025-07-28T14:53:53.885Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker pool response received {"action":"Worker pool response received","status":502,"ok":false,"workerId":8,"assignedNode":"jp-jp-023x-idx-10","attempt":1}
[WARN] [2025-07-28T14:53:53.886Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Worker pool error 502, marking node as failed {"url":"https://httpbin.org/ip","status":502,"workerId":8,"nodeTag":"jp-jp-023x-idx-10"}
[WARN] [2025-07-28T14:53:53.886Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Moving failed node to quarantine: jp-jp-023x-idx-10 {"reason":"HTTP 502 error"}
[INFO] [2025-07-28T14:53:53.887Z] [user:system] [task:N/A] - [TTS-NODE] Node moved to quarantine {"action":"Node moved to quarantine","nodeTag":"jp-jp-023x-idx-10","reason":"HTTP 502 error","quarantineType":"temporary","remainingHealthyNodes":111,"totalQuarantinedNodes":5}
[DEBUG] [2025-07-28T14:53:53.887Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker released {"workerId":8,"port":1088,"currentNode":"jp-jp-023x-idx-10"}
[INFO] [2025-07-28T14:53:53.887Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":8,"assignedNode":"jp-jp-023x-idx-10","attempt":1}
[ERROR] [2025-07-28T14:53:53.888Z] [user:system] [task:N/A] - Unknown error {"error":"Unknown error"}
[DEBUG] [2025-07-28T14:53:53.888Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Health check completed {"mode":"gateway","healthy":false,"status":502}
[WARN] [2025-07-28T14:53:53.888Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Network adapter health check failed
[WARN] [2025-07-28T14:53:53.888Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Moving failed node to quarantine: jp-jp-013x-idx-9 {"reason":"Health check failed"}
[INFO] [2025-07-28T14:53:53.889Z] [user:system] [task:N/A] - [TTS-NODE] Node moved to quarantine {"action":"Node moved to quarantine","nodeTag":"jp-jp-013x-idx-9","reason":"Health check failed","quarantineType":"temporary","remainingHealthyNodes":110,"totalQuarantinedNodes":6}
[DEBUG] [2025-07-28T14:53:53.889Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Health check completed {"networkHealthy":false,"timestamp":1753714431626}
[DEBUG] [2025-07-28T14:53:53.890Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Saved quarantine data for 6 nodes
[DEBUG] [2025-07-28T14:53:53.890Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Saved quarantine data for 6 nodes
[DEBUG] [2025-07-28T14:54:21.635Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Making gateway request {"method":"GET","url":"https://httpbin.org/ip","mode":"gateway"}
[DEBUG] [2025-07-28T14:54:21.635Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Testing node jp-jp-053x-idx-13 (attempt 1/110)
[DEBUG] [2025-07-28T14:54:21.785Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node jp-jp-053x-idx-13 {"nodeTag":"jp-jp-053x-idx-13","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-28T14:54:21.785Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Node jp-jp-053x-idx-13 passed health check
[DEBUG] [2025-07-28T14:54:21.786Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Commanding worker selector worker-selector-9 to switch to node: jp-jp-053x-idx-13
[INFO] [2025-07-28T14:54:21.786Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-9","targetNode":"jp-jp-053x-idx-13","originalNodeTag":"jp-jp-053x-idx-13","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-9","requestBody":{"name":"jp-jp-053x-idx-13"}}
[DEBUG] [2025-07-28T14:54:21.788Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Empty response from PUT /proxies/worker-selector-9
[INFO] [2025-07-28T14:54:21.788Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-9","newNode":"jp-jp-053x-idx-13","fixedNodeTag":"jp-jp-053x-idx-13"}
[DEBUG] [2025-07-28T14:54:21.789Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker selector worker-selector-9 successfully switched to node: jp-jp-053x-idx-13
[DEBUG] [2025-07-28T14:54:21.789Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker acquired with lazy health check {"workerId":9,"port":1089,"selector":"worker-selector-9","assignedNode":"jp-jp-053x-idx-13"}
[INFO] [2025-07-28T14:54:21.789Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":9,"workerPort":1089,"assignedNode":"jp-jp-053x-idx-13","method":"GET","url":"https://httpbin.org/ip","attempt":1,"maxAttempts":2}
[INFO] [2025-07-28T14:54:21.790Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1089","workerId":9,"assignedNode":"jp-jp-053x-idx-13","method":"GET","hasProxy":true,"proxyType":"dispatcher","attempt":1}
[ERROR] [2025-07-28T14:54:21.869Z] [user:system] [task:N/A] - [TTS-ERROR] Failed in worker-pool-request mode {"mode":"worker-pool-request","error":"fetch failed","method":"GET","url":"https://httpbin.org/ip","workerId":9,"assignedNode":"jp-jp-053x-idx-13","attempt":1,"maxAttempts":2} {"mode":"worker-pool-request","error":"[TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"GET\",\"url\":\"https://httpbin.org/ip\",\"workerId\":9,\"assignedNode\":\"jp-jp-053x-idx-13\",\"attempt\":1,\"maxAttempts\":2}","method":"GET","url":"https://httpbin.org/ip","workerId":9,"assignedNode":"jp-jp-053x-idx-13","attempt":1,"maxAttempts":2,"stack":"Error: [TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"GET\",\"url\":\"https://httpbin.org/ip\",\"workerId\":9,\"assignedNode\":\"jp-jp-053x-idx-13\",\"attempt\":1,\"maxAttempts\":2}\n    at TTSRouteLogger.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:270:18)\n    at Object.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:303:48)\n    at NetworkAdapter.requestViaGateway (D:\\myaitts\\backend\\src\\gateway\\adapters\\NetworkAdapter.js:207:23)\n    at "}
[WARN] [2025-07-28T14:54:21.869Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Worker pool network connectivity error {"error":"fetch failed","url":"https://httpbin.org/ip","workerId":9,"nodeTag":"jp-jp-053x-idx-13"}
[WARN] [2025-07-28T14:54:21.869Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Moving failed node to quarantine: jp-jp-053x-idx-13 {"reason":"Network error: fetch failed"}
[INFO] [2025-07-28T14:54:21.870Z] [user:system] [task:N/A] - [TTS-NODE] Node moved to quarantine {"action":"Node moved to quarantine","nodeTag":"jp-jp-053x-idx-13","reason":"Network error: fetch failed","quarantineType":"temporary","remainingHealthyNodes":109,"totalQuarantinedNodes":7}
[INFO] [2025-07-28T14:54:21.871Z] [user:system] [task:N/A] - [TTS-GATEWAY] Gateway request failed, retrying with new node {"action":"Gateway request failed, retrying with new node","attempt":1,"maxAttempts":2,"error":"fetch failed","failedNode":"jp-jp-053x-idx-13","willRetry":true}
[DEBUG] [2025-07-28T14:54:21.871Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker released {"workerId":9,"port":1089,"currentNode":"jp-jp-053x-idx-13"}
[INFO] [2025-07-28T14:54:21.871Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":9,"assignedNode":"jp-jp-053x-idx-13","attempt":1}
[DEBUG] [2025-07-28T14:54:21.872Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Testing node jp-jp-073x-idx-15 (attempt 1/109)
[DEBUG] [2025-07-28T14:54:21.873Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Saved quarantine data for 7 nodes
[DEBUG] [2025-07-28T14:54:21.970Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node jp-jp-073x-idx-15 {"nodeTag":"jp-jp-073x-idx-15","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-28T14:54:21.971Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Node jp-jp-073x-idx-15 passed health check
[DEBUG] [2025-07-28T14:54:21.971Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Commanding worker selector worker-selector-10 to switch to node: jp-jp-073x-idx-15
[INFO] [2025-07-28T14:54:21.971Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-10","targetNode":"jp-jp-073x-idx-15","originalNodeTag":"jp-jp-073x-idx-15","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-10","requestBody":{"name":"jp-jp-073x-idx-15"}}
[DEBUG] [2025-07-28T14:54:21.973Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Empty response from PUT /proxies/worker-selector-10
[INFO] [2025-07-28T14:54:21.973Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-10","newNode":"jp-jp-073x-idx-15","fixedNodeTag":"jp-jp-073x-idx-15"}
[DEBUG] [2025-07-28T14:54:21.973Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker selector worker-selector-10 successfully switched to node: jp-jp-073x-idx-15
[DEBUG] [2025-07-28T14:54:21.974Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker acquired with lazy health check {"workerId":10,"port":1090,"selector":"worker-selector-10","assignedNode":"jp-jp-073x-idx-15"}
[INFO] [2025-07-28T14:54:21.974Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":10,"workerPort":1090,"assignedNode":"jp-jp-073x-idx-15","method":"GET","url":"https://httpbin.org/ip","attempt":2,"maxAttempts":2}
[INFO] [2025-07-28T14:54:21.975Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1090","workerId":10,"assignedNode":"jp-jp-073x-idx-15","method":"GET","hasProxy":true,"proxyType":"dispatcher","attempt":2}
[ERROR] [2025-07-28T14:54:22.012Z] [user:system] [task:N/A] - [TTS-ERROR] Failed in worker-pool-request mode {"mode":"worker-pool-request","error":"fetch failed","method":"GET","url":"https://httpbin.org/ip","workerId":10,"assignedNode":"jp-jp-073x-idx-15","attempt":2,"maxAttempts":2} {"mode":"worker-pool-request","error":"[TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"GET\",\"url\":\"https://httpbin.org/ip\",\"workerId\":10,\"assignedNode\":\"jp-jp-073x-idx-15\",\"attempt\":2,\"maxAttempts\":2}","method":"GET","url":"https://httpbin.org/ip","workerId":10,"assignedNode":"jp-jp-073x-idx-15","attempt":2,"maxAttempts":2,"stack":"Error: [TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"GET\",\"url\":\"https://httpbin.org/ip\",\"workerId\":10,\"assignedNode\":\"jp-jp-073x-idx-15\",\"attempt\":2,\"maxAttempts\":2}\n    at TTSRouteLogger.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:270:18)\n    at Object.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:303:48)\n    at NetworkAdapter.requestViaGateway (D:\\myaitts\\backend\\src\\gateway\\adapters\\NetworkAdapter.js:207:23)\n    at"}
[WARN] [2025-07-28T14:54:22.013Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Worker pool network connectivity error {"error":"fetch failed","url":"https://httpbin.org/ip","workerId":10,"nodeTag":"jp-jp-073x-idx-15"}
[WARN] [2025-07-28T14:54:22.014Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Moving failed node to quarantine: jp-jp-073x-idx-15 {"reason":"Network error: fetch failed"}
[INFO] [2025-07-28T14:54:22.014Z] [user:system] [task:N/A] - [TTS-NODE] Node moved to quarantine {"action":"Node moved to quarantine","nodeTag":"jp-jp-073x-idx-15","reason":"Network error: fetch failed","quarantineType":"temporary","remainingHealthyNodes":108,"totalQuarantinedNodes":8}
[DEBUG] [2025-07-28T14:54:22.014Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker released {"workerId":10,"port":1090,"currentNode":"jp-jp-073x-idx-15"}
[INFO] [2025-07-28T14:54:22.015Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":10,"assignedNode":"jp-jp-073x-idx-15","attempt":2}
[ERROR] [2025-07-28T14:54:22.015Z] [user:system] [task:N/A] - Unknown error {"error":"Unknown error"}
[WARN] [2025-07-28T14:54:22.015Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Health check failed {"error":"fetch failed"}
[WARN] [2025-07-28T14:54:22.016Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Network adapter health check failed
[WARN] [2025-07-28T14:54:22.016Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Moving failed node to quarantine: jp-jp-013x-idx-9 {"reason":"Health check failed"}
[INFO] [2025-07-28T14:54:22.016Z] [user:system] [task:N/A] - [TTS-NODE] Node moved to quarantine {"action":"Node moved to quarantine","nodeTag":"jp-jp-013x-idx-9","reason":"Health check failed","quarantineType":"temporary","remainingHealthyNodes":108,"totalQuarantinedNodes":8}
[DEBUG] [2025-07-28T14:54:22.017Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Health check completed {"networkHealthy":false,"timestamp":1753714461634}
[DEBUG] [2025-07-28T14:54:22.017Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Saved quarantine data for 8 nodes
[DEBUG] [2025-07-28T14:54:22.018Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Saved quarantine data for 8 nodes
[ERROR] [2025-07-28T14:54:29.050Z] [user:system] [task:N/A] - [TTS-ERROR] Failed in worker-pool-request mode {"mode":"worker-pool-request","error":"The operation was aborted due to timeout","method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","workerId":6,"assignedNode":"jp-jp-013x-idx-9","attempt":1,"maxAttempts":2} {"mode":"worker-pool-request","error":"[TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"The operation was aborted due to timeout\",\"method\":\"POST\",\"url\":\"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1\",\"workerId\":6,\"assignedNode\":\"jp-jp-013x-idx-9\",\"attempt\":1,\"maxAttempts\":2}","method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","workerId":6,"assignedNode":"jp-jp-013x-idx-9","attempt":1,"maxAttempts":2,"stack":"Error: [TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"The operation was aborted due to timeout\",\"method\":\"POST\",\"url\":\"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1\",\"workerId\":6,\"assignedNode\":\"jp-jp-013x-idx-9\",\"attempt\":1,\"maxAttempts\":2}\n    at TTSRouteLogger.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:270:18)\n    at Object.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:303:48)\n    at NetworkAdapte"}
[INFO] [2025-07-28T14:54:29.050Z] [user:system] [task:N/A] - [TTS-GATEWAY] Gateway request failed, retrying with new node {"action":"Gateway request failed, retrying with new node","attempt":1,"maxAttempts":2,"error":"The operation was aborted due to timeout","failedNode":"jp-jp-013x-idx-9","willRetry":true}
[DEBUG] [2025-07-28T14:54:29.051Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker released {"workerId":6,"port":1086,"currentNode":"jp-jp-013x-idx-9"}
[INFO] [2025-07-28T14:54:29.051Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":6,"assignedNode":"jp-jp-013x-idx-9","attempt":1}
[DEBUG] [2025-07-28T14:54:29.051Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Testing node jp-jp-093x-idx-17 (attempt 1/108)
[DEBUG] [2025-07-28T14:54:29.183Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node jp-jp-093x-idx-17 {"nodeTag":"jp-jp-093x-idx-17","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-28T14:54:29.184Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Node jp-jp-093x-idx-17 passed health check
[DEBUG] [2025-07-28T14:54:29.185Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Commanding worker selector worker-selector-10 to switch to node: jp-jp-093x-idx-17
[INFO] [2025-07-28T14:54:29.185Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-10","targetNode":"jp-jp-093x-idx-17","originalNodeTag":"jp-jp-093x-idx-17","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-10","requestBody":{"name":"jp-jp-093x-idx-17"}}
[DEBUG] [2025-07-28T14:54:29.186Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Empty response from PUT /proxies/worker-selector-10
[INFO] [2025-07-28T14:54:29.187Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-10","newNode":"jp-jp-093x-idx-17","fixedNodeTag":"jp-jp-093x-idx-17"}
[DEBUG] [2025-07-28T14:54:29.187Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker selector worker-selector-10 successfully switched to node: jp-jp-093x-idx-17
[DEBUG] [2025-07-28T14:54:29.187Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker acquired with lazy health check {"workerId":10,"port":1090,"selector":"worker-selector-10","assignedNode":"jp-jp-093x-idx-17"}
[INFO] [2025-07-28T14:54:29.187Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":10,"workerPort":1090,"assignedNode":"jp-jp-093x-idx-17","method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","attempt":2,"maxAttempts":2}
[INFO] [2025-07-28T14:54:29.188Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1090","workerId":10,"assignedNode":"jp-jp-093x-idx-17","method":"POST","hasProxy":true,"proxyType":"dispatcher","attempt":2}
[ERROR] [2025-07-28T14:54:29.221Z] [user:system] [task:N/A] - [TTS-ERROR] Failed in worker-pool-request mode {"mode":"worker-pool-request","error":"fetch failed","method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","workerId":10,"assignedNode":"jp-jp-093x-idx-17","attempt":2,"maxAttempts":2} {"mode":"worker-pool-request","error":"[TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"POST\",\"url\":\"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1\",\"workerId\":10,\"assignedNode\":\"jp-jp-093x-idx-17\",\"attempt\":2,\"maxAttempts\":2}","method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","workerId":10,"assignedNode":"jp-jp-093x-idx-17","attempt":2,"maxAttempts":2,"stack":"Error: [TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"POST\",\"url\":\"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1\",\"workerId\":10,\"assignedNode\":\"jp-jp-093x-idx-17\",\"attempt\":2,\"maxAttempts\":2}\n    at TTSRouteLogger.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:270:18)\n    at Object.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:303:48)\n    at NetworkAdapter.requestViaGateway (D:\\my"}
[WARN] [2025-07-28T14:54:29.222Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Worker pool network connectivity error {"error":"fetch failed","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","workerId":10,"nodeTag":"jp-jp-093x-idx-17"}
[WARN] [2025-07-28T14:54:29.222Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Moving failed node to quarantine: jp-jp-093x-idx-17 {"reason":"Network error: fetch failed"}
[INFO] [2025-07-28T14:54:29.223Z] [user:system] [task:N/A] - [TTS-NODE] Node moved to quarantine {"action":"Node moved to quarantine","nodeTag":"jp-jp-093x-idx-17","reason":"Network error: fetch failed","quarantineType":"temporary","remainingHealthyNodes":107,"totalQuarantinedNodes":9}
[DEBUG] [2025-07-28T14:54:29.223Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker released {"workerId":10,"port":1090,"currentNode":"jp-jp-093x-idx-17"}
[INFO] [2025-07-28T14:54:29.224Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":10,"assignedNode":"jp-jp-093x-idx-17","attempt":2}
[ERROR] [2025-07-28T14:54:29.224Z] [user:system] [task:N/A] - Unknown error {"error":"Unknown error"}
[ERROR] [2025-07-28T14:54:29.224Z] [user:system] [task:N/A] - Unknown error {"error":"Unknown error"}
[ERROR] [2025-07-28T14:54:29.225Z] [user:system] [task:N/A] - [TTS-ERROR] Failed in gateway mode {"mode":"gateway","error":"fetch failed","totalDuration":"45328ms"} {"mode":"gateway","error":"[TTS-ERROR] Failed in gateway mode {\"mode\":\"gateway\",\"error\":\"fetch failed\",\"totalDuration\":\"45328ms\"}","totalDuration":"45328ms","stack":"Error: [TTS-ERROR] Failed in gateway mode {\"mode\":\"gateway\",\"error\":\"fetch failed\",\"totalDuration\":\"45328ms\"}\n    at TTSRouteLogger.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:270:18)\n    at Object.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:303:48)\n    at generateSpeechWithGateway (D:\\myaitts\\backend\\src\\utils\\ttsUtils.js:1322:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateSpeechSmart (D:\\myaitts\\backend\\src\\utils\\ttsU"}
[ERROR] [2025-07-28T14:54:29.225Z] [user:system] [task:N/A] - [TTS-ERROR] Failed in smart mode {"mode":"smart","error":"fetch failed","duration":"45328ms"} {"mode":"smart","error":"[TTS-ERROR] Failed in smart mode {\"mode\":\"smart\",\"error\":\"fetch failed\",\"duration\":\"45328ms\"}","duration":"45328ms","stack":"Error: [TTS-ERROR] Failed in smart mode {\"mode\":\"smart\",\"error\":\"fetch failed\",\"duration\":\"45328ms\"}\n    at TTSRouteLogger.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:270:18)\n    at Object.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:303:48)\n    at generateSpeechSmart (D:\\myaitts\\backend\\src\\utils\\ttsUtils.js:1462:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\myaitts\\backend\\src\\utils\\ttsUtils.js:310:29"}
[DEBUG] [2025-07-28T14:54:29.227Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Saved quarantine data for 9 nodes
[INFO] [2025-07-28T14:54:31.241Z] [user:system] [task:N/A] - [TTS-PROXY] Using fallback mode {"action":"Using fallback mode","strategy":"direct-first-then-proxy"}
[INFO] [2025-07-28T14:54:31.241Z] [user:system] [task:N/A] - [TTS-DIRECT] Attempting direct connection first {"action":"Attempting direct connection first"}
[INFO] [2025-07-28T14:54:31.242Z] [user:system] [task:N/A] - [TTS-DIRECT] Starting direct ElevenLabs request {"action":"Starting direct ElevenLabs request","voiceId":"tapn1QwocNXk3viVSowa","modelId":"eleven_v3","textLength":191}
[DEBUG] [2025-07-28T14:54:51.642Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Making gateway request {"method":"GET","url":"https://httpbin.org/ip","mode":"gateway"}
[DEBUG] [2025-07-28T14:54:51.643Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Testing node kr-kr-026x-idx-19 (attempt 1/107)
[DEBUG] [2025-07-28T14:54:51.794Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node kr-kr-026x-idx-19 {"nodeTag":"kr-kr-026x-idx-19","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-28T14:54:51.794Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Node kr-kr-026x-idx-19 passed health check
[DEBUG] [2025-07-28T14:54:51.794Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Commanding worker selector worker-selector-1 to switch to node: kr-kr-026x-idx-19
[INFO] [2025-07-28T14:54:51.795Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-1","targetNode":"kr-kr-026x-idx-19","originalNodeTag":"kr-kr-026x-idx-19","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-1","requestBody":{"name":"kr-kr-026x-idx-19"}}
[DEBUG] [2025-07-28T14:54:51.796Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Empty response from PUT /proxies/worker-selector-1
[INFO] [2025-07-28T14:54:51.796Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-1","newNode":"kr-kr-026x-idx-19","fixedNodeTag":"kr-kr-026x-idx-19"}
[DEBUG] [2025-07-28T14:54:51.797Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker selector worker-selector-1 successfully switched to node: kr-kr-026x-idx-19
[DEBUG] [2025-07-28T14:54:51.797Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker acquired with lazy health check {"workerId":1,"port":1081,"selector":"worker-selector-1","assignedNode":"kr-kr-026x-idx-19"}
[INFO] [2025-07-28T14:54:51.797Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":1,"workerPort":1081,"assignedNode":"kr-kr-026x-idx-19","method":"GET","url":"https://httpbin.org/ip","attempt":1,"maxAttempts":2}
[INFO] [2025-07-28T14:54:51.798Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1081","workerId":1,"assignedNode":"kr-kr-026x-idx-19","method":"GET","hasProxy":true,"proxyType":"dispatcher","attempt":1}
[INFO] [2025-07-28T14:54:53.083Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker pool response received {"action":"Worker pool response received","status":200,"ok":true,"workerId":1,"assignedNode":"kr-kr-026x-idx-19","attempt":1}
[DEBUG] [2025-07-28T14:54:53.084Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker released {"workerId":1,"port":1081,"currentNode":"kr-kr-026x-idx-19"}
[INFO] [2025-07-28T14:54:53.084Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":1,"assignedNode":"kr-kr-026x-idx-19","attempt":1}
[DEBUG] [2025-07-28T14:54:53.085Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Network request: GET https://httpbin.org/ip {"method":"GET","url":"https://httpbin.org/ip","status":200,"duration":"1443ms"}
[DEBUG] [2025-07-28T14:54:53.085Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Health check completed {"mode":"gateway","healthy":true,"status":200}
[DEBUG] [2025-07-28T14:54:53.085Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Health check completed {"networkHealthy":true,"timestamp":1753714491642}
[INFO] [2025-07-28T14:55:07.827Z] [user:system] [task:N/A] - [TTS-NETWORK] POST https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa","status":200,"duration":"36585ms","responseOk":true}
[INFO] [2025-07-28T14:55:09.861Z] [user:system] [task:N/A] - [TTS-DIRECT] Direct request successful {"action":"Direct request successful","audioSize":695529,"requestDuration":"36585ms","totalDuration":"38619ms"}
[INFO] [2025-07-28T14:55:09.861Z] [user:system] [task:N/A] - [TTS-SUCCESS] Generated via direct-fallback {"mode":"direct-fallback","audioSize":695529,"duration":"38621ms"}
[INFO] [2025-07-28T14:55:09.862Z] [user:system] [task:N/A] - [TTS-ROUTE] Route decision: gateway {"decision":"gateway","networkMode":"gateway","gatewayEnabled":true}
[INFO] [2025-07-28T14:55:09.862Z] [user:system] [task:N/A] - [TTS-GATEWAY] Starting gateway request {"action":"Starting gateway request","textLength":149,"voiceId":"tapn1QwocNXk3viVSowa","modelId":"eleven_v3"}
[INFO] [2025-07-28T14:55:09.863Z] [user:system] [task:N/A] - [TTS-GATEWAY] Sending request via network manager {"action":"Sending request via network manager","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","payloadSize":267}
[DEBUG] [2025-07-28T14:55:09.863Z] [user:system] [task:N/A] - [NETWORK-MANAGER] Making network request {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","mode":"gateway"}
[DEBUG] [2025-07-28T14:55:09.863Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Making gateway request {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","mode":"gateway"}
[DEBUG] [2025-07-28T14:55:09.864Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Testing node kr-kr-036x-idx-20 (attempt 1/107)
[DEBUG] [2025-07-28T14:55:10.001Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node kr-kr-036x-idx-20 {"nodeTag":"kr-kr-036x-idx-20","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-28T14:55:10.001Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Node kr-kr-036x-idx-20 passed health check
[DEBUG] [2025-07-28T14:55:10.002Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Commanding worker selector worker-selector-2 to switch to node: kr-kr-036x-idx-20
[INFO] [2025-07-28T14:55:10.002Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-2","targetNode":"kr-kr-036x-idx-20","originalNodeTag":"kr-kr-036x-idx-20","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-2","requestBody":{"name":"kr-kr-036x-idx-20"}}
[DEBUG] [2025-07-28T14:55:10.004Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Empty response from PUT /proxies/worker-selector-2
[INFO] [2025-07-28T14:55:10.004Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-2","newNode":"kr-kr-036x-idx-20","fixedNodeTag":"kr-kr-036x-idx-20"}
[DEBUG] [2025-07-28T14:55:10.004Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker selector worker-selector-2 successfully switched to node: kr-kr-036x-idx-20
[DEBUG] [2025-07-28T14:55:10.005Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker acquired with lazy health check {"workerId":2,"port":1082,"selector":"worker-selector-2","assignedNode":"kr-kr-036x-idx-20"}
[INFO] [2025-07-28T14:55:10.005Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":2,"workerPort":1082,"assignedNode":"kr-kr-036x-idx-20","method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","attempt":1,"maxAttempts":2}
[INFO] [2025-07-28T14:55:10.005Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1082","workerId":2,"assignedNode":"kr-kr-036x-idx-20","method":"POST","hasProxy":true,"proxyType":"dispatcher","attempt":1}
[DEBUG] [2025-07-28T14:55:21.653Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Making gateway request {"method":"GET","url":"https://httpbin.org/ip","mode":"gateway"}
[DEBUG] [2025-07-28T14:55:21.654Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Testing node kr-kr-046x-idx-21 (attempt 1/107)
[DEBUG] [2025-07-28T14:55:21.789Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node kr-kr-046x-idx-21 {"nodeTag":"kr-kr-046x-idx-21","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-28T14:55:21.790Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Node kr-kr-046x-idx-21 passed health check
[DEBUG] [2025-07-28T14:55:21.790Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Commanding worker selector worker-selector-4 to switch to node: kr-kr-046x-idx-21
[INFO] [2025-07-28T14:55:21.791Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-4","targetNode":"kr-kr-046x-idx-21","originalNodeTag":"kr-kr-046x-idx-21","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-4","requestBody":{"name":"kr-kr-046x-idx-21"}}
[DEBUG] [2025-07-28T14:55:21.792Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Empty response from PUT /proxies/worker-selector-4
[INFO] [2025-07-28T14:55:21.792Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-4","newNode":"kr-kr-046x-idx-21","fixedNodeTag":"kr-kr-046x-idx-21"}
[DEBUG] [2025-07-28T14:55:21.793Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker selector worker-selector-4 successfully switched to node: kr-kr-046x-idx-21
[DEBUG] [2025-07-28T14:55:21.793Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker acquired with lazy health check {"workerId":4,"port":1084,"selector":"worker-selector-4","assignedNode":"kr-kr-046x-idx-21"}
[INFO] [2025-07-28T14:55:21.793Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":4,"workerPort":1084,"assignedNode":"kr-kr-046x-idx-21","method":"GET","url":"https://httpbin.org/ip","attempt":1,"maxAttempts":2}
[INFO] [2025-07-28T14:55:21.793Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1084","workerId":4,"assignedNode":"kr-kr-046x-idx-21","method":"GET","hasProxy":true,"proxyType":"dispatcher","attempt":1}
[ERROR] [2025-07-28T14:55:21.866Z] [user:system] [task:N/A] - [TTS-ERROR] Failed in worker-pool-request mode {"mode":"worker-pool-request","error":"fetch failed","method":"GET","url":"https://httpbin.org/ip","workerId":4,"assignedNode":"kr-kr-046x-idx-21","attempt":1,"maxAttempts":2} {"mode":"worker-pool-request","error":"[TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"GET\",\"url\":\"https://httpbin.org/ip\",\"workerId\":4,\"assignedNode\":\"kr-kr-046x-idx-21\",\"attempt\":1,\"maxAttempts\":2}","method":"GET","url":"https://httpbin.org/ip","workerId":4,"assignedNode":"kr-kr-046x-idx-21","attempt":1,"maxAttempts":2,"stack":"Error: [TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"GET\",\"url\":\"https://httpbin.org/ip\",\"workerId\":4,\"assignedNode\":\"kr-kr-046x-idx-21\",\"attempt\":1,\"maxAttempts\":2}\n    at TTSRouteLogger.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:270:18)\n    at Object.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:303:48)\n    at NetworkAdapter.requestViaGateway (D:\\myaitts\\backend\\src\\gateway\\adapters\\NetworkAdapter.js:207:23)\n    at "}
[WARN] [2025-07-28T14:55:21.866Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Worker pool network connectivity error {"error":"fetch failed","url":"https://httpbin.org/ip","workerId":4,"nodeTag":"kr-kr-046x-idx-21"}
[WARN] [2025-07-28T14:55:21.867Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Moving failed node to quarantine: kr-kr-046x-idx-21 {"reason":"Network error: fetch failed"}
[INFO] [2025-07-28T14:55:21.867Z] [user:system] [task:N/A] - [TTS-NODE] Node moved to quarantine {"action":"Node moved to quarantine","nodeTag":"kr-kr-046x-idx-21","reason":"Network error: fetch failed","quarantineType":"temporary","remainingHealthyNodes":106,"totalQuarantinedNodes":10}
[INFO] [2025-07-28T14:55:21.867Z] [user:system] [task:N/A] - [TTS-GATEWAY] Gateway request failed, retrying with new node {"action":"Gateway request failed, retrying with new node","attempt":1,"maxAttempts":2,"error":"fetch failed","failedNode":"kr-kr-046x-idx-21","willRetry":true}
[DEBUG] [2025-07-28T14:55:21.868Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker released {"workerId":4,"port":1084,"currentNode":"kr-kr-046x-idx-21"}
[INFO] [2025-07-28T14:55:21.868Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":4,"assignedNode":"kr-kr-046x-idx-21","attempt":1}
[DEBUG] [2025-07-28T14:55:21.868Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Testing node kr-kr-066x-idx-23 (attempt 1/106)
[DEBUG] [2025-07-28T14:55:21.869Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Saved quarantine data for 10 nodes
[DEBUG] [2025-07-28T14:55:21.954Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node kr-kr-066x-idx-23 {"nodeTag":"kr-kr-066x-idx-23","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-28T14:55:21.955Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Node kr-kr-066x-idx-23 passed health check
[DEBUG] [2025-07-28T14:55:21.955Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Commanding worker selector worker-selector-5 to switch to node: kr-kr-066x-idx-23
[INFO] [2025-07-28T14:55:21.956Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-5","targetNode":"kr-kr-066x-idx-23","originalNodeTag":"kr-kr-066x-idx-23","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-5","requestBody":{"name":"kr-kr-066x-idx-23"}}
[DEBUG] [2025-07-28T14:55:21.957Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Empty response from PUT /proxies/worker-selector-5
[INFO] [2025-07-28T14:55:21.957Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-5","newNode":"kr-kr-066x-idx-23","fixedNodeTag":"kr-kr-066x-idx-23"}
[DEBUG] [2025-07-28T14:55:21.958Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker selector worker-selector-5 successfully switched to node: kr-kr-066x-idx-23
[DEBUG] [2025-07-28T14:55:21.958Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker acquired with lazy health check {"workerId":5,"port":1085,"selector":"worker-selector-5","assignedNode":"kr-kr-066x-idx-23"}
[INFO] [2025-07-28T14:55:21.958Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":5,"workerPort":1085,"assignedNode":"kr-kr-066x-idx-23","method":"GET","url":"https://httpbin.org/ip","attempt":2,"maxAttempts":2}
[INFO] [2025-07-28T14:55:21.959Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1085","workerId":5,"assignedNode":"kr-kr-066x-idx-23","method":"GET","hasProxy":true,"proxyType":"dispatcher","attempt":2}
[ERROR] [2025-07-28T14:55:22.008Z] [user:system] [task:N/A] - [TTS-ERROR] Failed in worker-pool-request mode {"mode":"worker-pool-request","error":"fetch failed","method":"GET","url":"https://httpbin.org/ip","workerId":5,"assignedNode":"kr-kr-066x-idx-23","attempt":2,"maxAttempts":2} {"mode":"worker-pool-request","error":"[TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"GET\",\"url\":\"https://httpbin.org/ip\",\"workerId\":5,\"assignedNode\":\"kr-kr-066x-idx-23\",\"attempt\":2,\"maxAttempts\":2}","method":"GET","url":"https://httpbin.org/ip","workerId":5,"assignedNode":"kr-kr-066x-idx-23","attempt":2,"maxAttempts":2,"stack":"Error: [TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"GET\",\"url\":\"https://httpbin.org/ip\",\"workerId\":5,\"assignedNode\":\"kr-kr-066x-idx-23\",\"attempt\":2,\"maxAttempts\":2}\n    at TTSRouteLogger.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:270:18)\n    at Object.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:303:48)\n    at NetworkAdapter.requestViaGateway (D:\\myaitts\\backend\\src\\gateway\\adapters\\NetworkAdapter.js:207:23)\n    at "}
[WARN] [2025-07-28T14:55:22.009Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Worker pool network connectivity error {"error":"fetch failed","url":"https://httpbin.org/ip","workerId":5,"nodeTag":"kr-kr-066x-idx-23"}
[WARN] [2025-07-28T14:55:22.009Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Moving failed node to quarantine: kr-kr-066x-idx-23 {"reason":"Network error: fetch failed"}
[INFO] [2025-07-28T14:55:22.010Z] [user:system] [task:N/A] - [TTS-NODE] Node moved to quarantine {"action":"Node moved to quarantine","nodeTag":"kr-kr-066x-idx-23","reason":"Network error: fetch failed","quarantineType":"temporary","remainingHealthyNodes":105,"totalQuarantinedNodes":11}
[DEBUG] [2025-07-28T14:55:22.010Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker released {"workerId":5,"port":1085,"currentNode":"kr-kr-066x-idx-23"}
[INFO] [2025-07-28T14:55:22.010Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":5,"assignedNode":"kr-kr-066x-idx-23","attempt":2}
[ERROR] [2025-07-28T14:55:22.010Z] [user:system] [task:N/A] - Unknown error {"error":"Unknown error"}
[WARN] [2025-07-28T14:55:22.011Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Health check failed {"error":"fetch failed"}
[WARN] [2025-07-28T14:55:22.011Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Network adapter health check failed
[WARN] [2025-07-28T14:55:22.011Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Moving failed node to quarantine: kr-kr-036x-idx-20 {"reason":"Health check failed"}
[INFO] [2025-07-28T14:55:22.012Z] [user:system] [task:N/A] - [TTS-NODE] Node moved to quarantine {"action":"Node moved to quarantine","nodeTag":"kr-kr-036x-idx-20","reason":"Health check failed","quarantineType":"temporary","remainingHealthyNodes":104,"totalQuarantinedNodes":12}
[DEBUG] [2025-07-28T14:55:22.012Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Health check completed {"networkHealthy":false,"timestamp":1753714521653}
[DEBUG] [2025-07-28T14:55:22.014Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Saved quarantine data for 12 nodes
[DEBUG] [2025-07-28T14:55:22.014Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Saved quarantine data for 12 nodes
[INFO] [2025-07-28T14:55:22.118Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker pool response received {"action":"Worker pool response received","status":200,"ok":true,"workerId":2,"assignedNode":"kr-kr-036x-idx-20","attempt":1}
[DEBUG] [2025-07-28T14:55:22.119Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker released {"workerId":2,"port":1082,"currentNode":"kr-kr-036x-idx-20"}
[INFO] [2025-07-28T14:55:22.119Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":2,"assignedNode":"kr-kr-036x-idx-20","attempt":1}
[WARN] [2025-07-28T14:55:22.120Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Slow network request: POST https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1 {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","status":200,"duration":"12257ms"}
[INFO] [2025-07-28T14:55:22.120Z] [user:system] [task:N/A] - [TTS-NETWORK] POST https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa","status":200,"duration":"12257ms","responseOk":true}
[INFO] [2025-07-28T14:55:22.265Z] [user:system] [task:N/A] - [TTS-GATEWAY] Request completed successfully {"action":"Request completed successfully","audioSize":241207,"totalDuration":"12403ms","requestDuration":"12257ms"}
[INFO] [2025-07-28T14:55:22.266Z] [user:system] [task:N/A] - [TTS-SUCCESS] Generated via gateway {"mode":"gateway","audioSize":241207,"duration":"12404ms"}
[INFO] [2025-07-28T14:55:24.753Z] [user:system] [task:N/A] - GET /stream/5cb147a3-1bf3-410f-a6bb-3acf599add9f {"status":200,"duration":"6ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::1"}
[DEBUG] [2025-07-28T14:55:51.666Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Making gateway request {"method":"GET","url":"https://httpbin.org/ip","mode":"gateway"}
[DEBUG] [2025-07-28T14:55:51.667Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Testing node kr-kr-096x-idx-26 (attempt 1/104)
[DEBUG] [2025-07-28T14:55:51.818Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node kr-kr-096x-idx-26 {"nodeTag":"kr-kr-096x-idx-26","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-28T14:55:51.818Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Node kr-kr-096x-idx-26 passed health check
[DEBUG] [2025-07-28T14:55:51.819Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Commanding worker selector worker-selector-5 to switch to node: kr-kr-096x-idx-26
[INFO] [2025-07-28T14:55:51.819Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-5","targetNode":"kr-kr-096x-idx-26","originalNodeTag":"kr-kr-096x-idx-26","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-5","requestBody":{"name":"kr-kr-096x-idx-26"}}
[DEBUG] [2025-07-28T14:55:51.821Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Empty response from PUT /proxies/worker-selector-5
[INFO] [2025-07-28T14:55:51.821Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-5","newNode":"kr-kr-096x-idx-26","fixedNodeTag":"kr-kr-096x-idx-26"}
[DEBUG] [2025-07-28T14:55:51.822Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker selector worker-selector-5 successfully switched to node: kr-kr-096x-idx-26
[DEBUG] [2025-07-28T14:55:51.822Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker acquired with lazy health check {"workerId":5,"port":1085,"selector":"worker-selector-5","assignedNode":"kr-kr-096x-idx-26"}
[INFO] [2025-07-28T14:55:51.822Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":5,"workerPort":1085,"assignedNode":"kr-kr-096x-idx-26","method":"GET","url":"https://httpbin.org/ip","attempt":1,"maxAttempts":2}
[INFO] [2025-07-28T14:55:51.823Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1085","workerId":5,"assignedNode":"kr-kr-096x-idx-26","method":"GET","hasProxy":true,"proxyType":"dispatcher","attempt":1}
[ERROR] [2025-07-28T14:55:51.860Z] [user:system] [task:N/A] - [TTS-ERROR] Failed in worker-pool-request mode {"mode":"worker-pool-request","error":"fetch failed","method":"GET","url":"https://httpbin.org/ip","workerId":5,"assignedNode":"kr-kr-096x-idx-26","attempt":1,"maxAttempts":2} {"mode":"worker-pool-request","error":"[TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"GET\",\"url\":\"https://httpbin.org/ip\",\"workerId\":5,\"assignedNode\":\"kr-kr-096x-idx-26\",\"attempt\":1,\"maxAttempts\":2}","method":"GET","url":"https://httpbin.org/ip","workerId":5,"assignedNode":"kr-kr-096x-idx-26","attempt":1,"maxAttempts":2,"stack":"Error: [TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"GET\",\"url\":\"https://httpbin.org/ip\",\"workerId\":5,\"assignedNode\":\"kr-kr-096x-idx-26\",\"attempt\":1,\"maxAttempts\":2}\n    at TTSRouteLogger.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:270:18)\n    at Object.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:303:48)\n    at NetworkAdapter.requestViaGateway (D:\\myaitts\\backend\\src\\gateway\\adapters\\NetworkAdapter.js:207:23)\n    at "}
[WARN] [2025-07-28T14:55:51.860Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Worker pool network connectivity error {"error":"fetch failed","url":"https://httpbin.org/ip","workerId":5,"nodeTag":"kr-kr-096x-idx-26"}
[WARN] [2025-07-28T14:55:51.860Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Moving failed node to quarantine: kr-kr-096x-idx-26 {"reason":"Network error: fetch failed"}
[INFO] [2025-07-28T14:55:51.861Z] [user:system] [task:N/A] - [TTS-NODE] Node moved to quarantine {"action":"Node moved to quarantine","nodeTag":"kr-kr-096x-idx-26","reason":"Network error: fetch failed","quarantineType":"temporary","remainingHealthyNodes":103,"totalQuarantinedNodes":13}
[INFO] [2025-07-28T14:55:51.862Z] [user:system] [task:N/A] - [TTS-GATEWAY] Gateway request failed, retrying with new node {"action":"Gateway request failed, retrying with new node","attempt":1,"maxAttempts":2,"error":"fetch failed","failedNode":"kr-kr-096x-idx-26","willRetry":true}
[DEBUG] [2025-07-28T14:55:51.862Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker released {"workerId":5,"port":1085,"currentNode":"kr-kr-096x-idx-26"}
[INFO] [2025-07-28T14:55:51.862Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":5,"assignedNode":"kr-kr-096x-idx-26","attempt":1}
[DEBUG] [2025-07-28T14:55:51.863Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Testing node us-us-023x-idx-28 (attempt 1/103)
[DEBUG] [2025-07-28T14:55:51.863Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Saved quarantine data for 13 nodes
[DEBUG] [2025-07-28T14:55:51.961Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node us-us-023x-idx-28 {"nodeTag":"us-us-023x-idx-28","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-28T14:55:51.962Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Node us-us-023x-idx-28 passed health check
[DEBUG] [2025-07-28T14:55:51.962Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Commanding worker selector worker-selector-6 to switch to node: us-us-023x-idx-28
[INFO] [2025-07-28T14:55:51.963Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-6","targetNode":"us-us-023x-idx-28","originalNodeTag":"us-us-023x-idx-28","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-6","requestBody":{"name":"us-us-023x-idx-28"}}
[DEBUG] [2025-07-28T14:55:51.965Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Empty response from PUT /proxies/worker-selector-6
[INFO] [2025-07-28T14:55:51.965Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-6","newNode":"us-us-023x-idx-28","fixedNodeTag":"us-us-023x-idx-28"}
[DEBUG] [2025-07-28T14:55:51.965Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker selector worker-selector-6 successfully switched to node: us-us-023x-idx-28
[DEBUG] [2025-07-28T14:55:51.966Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker acquired with lazy health check {"workerId":6,"port":1086,"selector":"worker-selector-6","assignedNode":"us-us-023x-idx-28"}
[INFO] [2025-07-28T14:55:51.966Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":6,"workerPort":1086,"assignedNode":"us-us-023x-idx-28","method":"GET","url":"https://httpbin.org/ip","attempt":2,"maxAttempts":2}
[INFO] [2025-07-28T14:55:51.966Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1086","workerId":6,"assignedNode":"us-us-023x-idx-28","method":"GET","hasProxy":true,"proxyType":"dispatcher","attempt":2}
[INFO] [2025-07-28T14:55:55.348Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker pool response received {"action":"Worker pool response received","status":200,"ok":true,"workerId":6,"assignedNode":"us-us-023x-idx-28","attempt":2}
[DEBUG] [2025-07-28T14:55:55.348Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker released {"workerId":6,"port":1086,"currentNode":"us-us-023x-idx-28"}
[INFO] [2025-07-28T14:55:55.349Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":6,"assignedNode":"us-us-023x-idx-28","attempt":2}
[DEBUG] [2025-07-28T14:55:55.349Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Network request: GET https://httpbin.org/ip {"method":"GET","url":"https://httpbin.org/ip","status":200,"duration":"3683ms"}
[DEBUG] [2025-07-28T14:55:55.349Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Health check completed {"mode":"gateway","healthy":true,"status":200}
[DEBUG] [2025-07-28T14:55:55.349Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Health check completed {"networkHealthy":true,"timestamp":1753714551666}
[DEBUG] [2025-07-28T14:56:21.672Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Making gateway request {"method":"GET","url":"https://httpbin.org/ip","mode":"gateway"}
[DEBUG] [2025-07-28T14:56:21.673Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Testing node us-us-033x-idx-29 (attempt 1/103)
[DEBUG] [2025-07-28T14:56:21.809Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node us-us-033x-idx-29 {"nodeTag":"us-us-033x-idx-29","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-28T14:56:21.810Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Node us-us-033x-idx-29 passed health check
[DEBUG] [2025-07-28T14:56:21.811Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Commanding worker selector worker-selector-7 to switch to node: us-us-033x-idx-29
[INFO] [2025-07-28T14:56:21.811Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-7","targetNode":"us-us-033x-idx-29","originalNodeTag":"us-us-033x-idx-29","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-7","requestBody":{"name":"us-us-033x-idx-29"}}
[DEBUG] [2025-07-28T14:56:21.813Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Empty response from PUT /proxies/worker-selector-7
[INFO] [2025-07-28T14:56:21.813Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-7","newNode":"us-us-033x-idx-29","fixedNodeTag":"us-us-033x-idx-29"}
[DEBUG] [2025-07-28T14:56:21.814Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker selector worker-selector-7 successfully switched to node: us-us-033x-idx-29
[DEBUG] [2025-07-28T14:56:21.814Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker acquired with lazy health check {"workerId":7,"port":1087,"selector":"worker-selector-7","assignedNode":"us-us-033x-idx-29"}
[INFO] [2025-07-28T14:56:21.814Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":7,"workerPort":1087,"assignedNode":"us-us-033x-idx-29","method":"GET","url":"https://httpbin.org/ip","attempt":1,"maxAttempts":2}
[INFO] [2025-07-28T14:56:21.815Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1087","workerId":7,"assignedNode":"us-us-033x-idx-29","method":"GET","hasProxy":true,"proxyType":"dispatcher","attempt":1}
[INFO] [2025-07-28T14:56:23.375Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker pool response received {"action":"Worker pool response received","status":200,"ok":true,"workerId":7,"assignedNode":"us-us-033x-idx-29","attempt":1}
[DEBUG] [2025-07-28T14:56:23.375Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker released {"workerId":7,"port":1087,"currentNode":"us-us-033x-idx-29"}
[INFO] [2025-07-28T14:56:23.376Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":7,"assignedNode":"us-us-033x-idx-29","attempt":1}
[DEBUG] [2025-07-28T14:56:23.376Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Network request: GET https://httpbin.org/ip {"method":"GET","url":"https://httpbin.org/ip","status":200,"duration":"1704ms"}
[DEBUG] [2025-07-28T14:56:23.376Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Health check completed {"mode":"gateway","healthy":true,"status":200}
[DEBUG] [2025-07-28T14:56:23.376Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Health check completed {"networkHealthy":true,"timestamp":1753714581672}
[DEBUG] [2025-07-28T14:56:51.682Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Making gateway request {"method":"GET","url":"https://httpbin.org/ip","mode":"gateway"}
[DEBUG] [2025-07-28T14:56:51.682Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Testing node us-us-043x-idx-30 (attempt 1/103)
[DEBUG] [2025-07-28T14:56:51.836Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node us-us-043x-idx-30 {"nodeTag":"us-us-043x-idx-30","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-28T14:56:51.836Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Node us-us-043x-idx-30 passed health check
[DEBUG] [2025-07-28T14:56:51.837Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Commanding worker selector worker-selector-8 to switch to node: us-us-043x-idx-30
[INFO] [2025-07-28T14:56:51.837Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-8","targetNode":"us-us-043x-idx-30","originalNodeTag":"us-us-043x-idx-30","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-8","requestBody":{"name":"us-us-043x-idx-30"}}
[DEBUG] [2025-07-28T14:56:51.839Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Empty response from PUT /proxies/worker-selector-8
[INFO] [2025-07-28T14:56:51.839Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-8","newNode":"us-us-043x-idx-30","fixedNodeTag":"us-us-043x-idx-30"}
[DEBUG] [2025-07-28T14:56:51.840Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker selector worker-selector-8 successfully switched to node: us-us-043x-idx-30
[DEBUG] [2025-07-28T14:56:51.840Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker acquired with lazy health check {"workerId":8,"port":1088,"selector":"worker-selector-8","assignedNode":"us-us-043x-idx-30"}
[INFO] [2025-07-28T14:56:51.840Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":8,"workerPort":1088,"assignedNode":"us-us-043x-idx-30","method":"GET","url":"https://httpbin.org/ip","attempt":1,"maxAttempts":2}
[INFO] [2025-07-28T14:56:51.841Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1088","workerId":8,"assignedNode":"us-us-043x-idx-30","method":"GET","hasProxy":true,"proxyType":"dispatcher","attempt":1}
[ERROR] [2025-07-28T14:56:51.988Z] [user:system] [task:N/A] - [TTS-ERROR] Failed in worker-pool-request mode {"mode":"worker-pool-request","error":"fetch failed","method":"GET","url":"https://httpbin.org/ip","workerId":8,"assignedNode":"us-us-043x-idx-30","attempt":1,"maxAttempts":2} {"mode":"worker-pool-request","error":"[TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"GET\",\"url\":\"https://httpbin.org/ip\",\"workerId\":8,\"assignedNode\":\"us-us-043x-idx-30\",\"attempt\":1,\"maxAttempts\":2}","method":"GET","url":"https://httpbin.org/ip","workerId":8,"assignedNode":"us-us-043x-idx-30","attempt":1,"maxAttempts":2,"stack":"Error: [TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"GET\",\"url\":\"https://httpbin.org/ip\",\"workerId\":8,\"assignedNode\":\"us-us-043x-idx-30\",\"attempt\":1,\"maxAttempts\":2}\n    at TTSRouteLogger.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:270:18)\n    at Object.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:303:48)\n    at NetworkAdapter.requestViaGateway (D:\\myaitts\\backend\\src\\gateway\\adapters\\NetworkAdapter.js:207:23)\n    at "}
[WARN] [2025-07-28T14:56:51.989Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Worker pool network connectivity error {"error":"fetch failed","url":"https://httpbin.org/ip","workerId":8,"nodeTag":"us-us-043x-idx-30"}
[WARN] [2025-07-28T14:56:51.989Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Moving failed node to quarantine: us-us-043x-idx-30 {"reason":"Network error: fetch failed"}
[INFO] [2025-07-28T14:56:51.990Z] [user:system] [task:N/A] - [TTS-NODE] Node moved to quarantine {"action":"Node moved to quarantine","nodeTag":"us-us-043x-idx-30","reason":"Network error: fetch failed","quarantineType":"temporary","remainingHealthyNodes":102,"totalQuarantinedNodes":14}
[INFO] [2025-07-28T14:56:51.990Z] [user:system] [task:N/A] - [TTS-GATEWAY] Gateway request failed, retrying with new node {"action":"Gateway request failed, retrying with new node","attempt":1,"maxAttempts":2,"error":"fetch failed","failedNode":"us-us-043x-idx-30","willRetry":true}
[DEBUG] [2025-07-28T14:56:51.990Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker released {"workerId":8,"port":1088,"currentNode":"us-us-043x-idx-30"}
[INFO] [2025-07-28T14:56:51.991Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":8,"assignedNode":"us-us-043x-idx-30","attempt":1}
[DEBUG] [2025-07-28T14:56:51.991Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Testing node us-us-063x-idx-32 (attempt 1/102)
[DEBUG] [2025-07-28T14:56:51.992Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Saved quarantine data for 14 nodes
[DEBUG] [2025-07-28T14:56:52.091Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node us-us-063x-idx-32 {"nodeTag":"us-us-063x-idx-32","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-28T14:56:52.091Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Node us-us-063x-idx-32 passed health check
[DEBUG] [2025-07-28T14:56:52.091Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Commanding worker selector worker-selector-9 to switch to node: us-us-063x-idx-32
[INFO] [2025-07-28T14:56:52.092Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-9","targetNode":"us-us-063x-idx-32","originalNodeTag":"us-us-063x-idx-32","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-9","requestBody":{"name":"us-us-063x-idx-32"}}
[DEBUG] [2025-07-28T14:56:52.093Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Empty response from PUT /proxies/worker-selector-9
[INFO] [2025-07-28T14:56:52.094Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-9","newNode":"us-us-063x-idx-32","fixedNodeTag":"us-us-063x-idx-32"}
[DEBUG] [2025-07-28T14:56:52.094Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker selector worker-selector-9 successfully switched to node: us-us-063x-idx-32
[DEBUG] [2025-07-28T14:56:52.094Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker acquired with lazy health check {"workerId":9,"port":1089,"selector":"worker-selector-9","assignedNode":"us-us-063x-idx-32"}
[INFO] [2025-07-28T14:56:52.094Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":9,"workerPort":1089,"assignedNode":"us-us-063x-idx-32","method":"GET","url":"https://httpbin.org/ip","attempt":2,"maxAttempts":2}
[INFO] [2025-07-28T14:56:52.095Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1089","workerId":9,"assignedNode":"us-us-063x-idx-32","method":"GET","hasProxy":true,"proxyType":"dispatcher","attempt":2}
[ERROR] [2025-07-28T14:56:52.223Z] [user:system] [task:N/A] - [TTS-ERROR] Failed in worker-pool-request mode {"mode":"worker-pool-request","error":"fetch failed","method":"GET","url":"https://httpbin.org/ip","workerId":9,"assignedNode":"us-us-063x-idx-32","attempt":2,"maxAttempts":2} {"mode":"worker-pool-request","error":"[TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"GET\",\"url\":\"https://httpbin.org/ip\",\"workerId\":9,\"assignedNode\":\"us-us-063x-idx-32\",\"attempt\":2,\"maxAttempts\":2}","method":"GET","url":"https://httpbin.org/ip","workerId":9,"assignedNode":"us-us-063x-idx-32","attempt":2,"maxAttempts":2,"stack":"Error: [TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"GET\",\"url\":\"https://httpbin.org/ip\",\"workerId\":9,\"assignedNode\":\"us-us-063x-idx-32\",\"attempt\":2,\"maxAttempts\":2}\n    at TTSRouteLogger.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:270:18)\n    at Object.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:303:48)\n    at NetworkAdapter.requestViaGateway (D:\\myaitts\\backend\\src\\gateway\\adapters\\NetworkAdapter.js:207:23)\n    at "}
[WARN] [2025-07-28T14:56:52.223Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Worker pool network connectivity error {"error":"fetch failed","url":"https://httpbin.org/ip","workerId":9,"nodeTag":"us-us-063x-idx-32"}
[WARN] [2025-07-28T14:56:52.224Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Moving failed node to quarantine: us-us-063x-idx-32 {"reason":"Network error: fetch failed"}
[INFO] [2025-07-28T14:56:52.224Z] [user:system] [task:N/A] - [TTS-NODE] Node moved to quarantine {"action":"Node moved to quarantine","nodeTag":"us-us-063x-idx-32","reason":"Network error: fetch failed","quarantineType":"temporary","remainingHealthyNodes":101,"totalQuarantinedNodes":15}
[DEBUG] [2025-07-28T14:56:52.225Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker released {"workerId":9,"port":1089,"currentNode":"us-us-063x-idx-32"}
[INFO] [2025-07-28T14:56:52.225Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":9,"assignedNode":"us-us-063x-idx-32","attempt":2}
[ERROR] [2025-07-28T14:56:52.225Z] [user:system] [task:N/A] - Unknown error {"error":"Unknown error"}
[WARN] [2025-07-28T14:56:52.225Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Health check failed {"error":"fetch failed"}
[WARN] [2025-07-28T14:56:52.226Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Network adapter health check failed
[DEBUG] [2025-07-28T14:56:52.226Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Health check completed {"networkHealthy":false,"timestamp":1753714611681}
[DEBUG] [2025-07-28T14:56:52.227Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Saved quarantine data for 15 nodes
[INFO] [2025-07-28T14:56:59.624Z] [user:system] [task:N/A] - SIGINT received, shutting down gracefully
[INFO] [2025-07-28T14:56:59.626Z] [user:system] [task:N/A] - All connections closed successfully
